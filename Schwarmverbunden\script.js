// Main JavaScript file for DAS NEUE JETZT website

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// Initialize all website functionality
function initializeWebsite() {
    // Profile image modal (if on <PERSON> Sophie page)
    setupProfileImageModal();

    // Initialize flip cards
    setupFlipCards();

    // Initialize accordion
    setupAccordion();

    // Initialize scroll indicators
    setupScrollIndicators();

    // Optimize for mobile devices
    optimizeForMobile();
}

// Setup Compact Flip Cards
function setupFlipCards() {
    const flipCards = document.querySelectorAll('.flip-card-compact');

    flipCards.forEach(card => {
        const inner = card.querySelector('.flip-card-inner');
        let isFlipped = false;

        // Handle click/tap events for mobile and desktop
        card.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle flip state
            isFlipped = !isFlipped;

            if (isFlipped) {
                inner.style.transform = 'rotateY(180deg)';
                card.classList.add('flipped');
            } else {
                inner.style.transform = 'rotateY(0deg)';
                card.classList.remove('flipped');
            }
        });

        // For desktop: also handle hover (but don't interfere with click)
        if (!isTouchDevice()) {
            card.addEventListener('mouseenter', function() {
                if (!isFlipped) {
                    inner.style.transform = 'rotateY(180deg)';
                }
            });

            card.addEventListener('mouseleave', function() {
                if (!isFlipped) {
                    inner.style.transform = 'rotateY(0deg)';
                }
            });
        }

        // Add keyboard accessibility
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'button');

        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                card.click();
            }
        });
    });
}

// Helper function to detect touch devices
function isTouchDevice() {
    return (('ontouchstart' in window) ||
            (navigator.maxTouchPoints > 0) ||
            (navigator.msMaxTouchPoints > 0));
}

// Profile Image Modal (for Jana Sophie page)
function setupProfileImageModal() {
    const profileImage = document.querySelector('.profile-image');

    if (profileImage) {
        // Container für das Modal-Bild erstellen
        const imageModalContainer = document.createElement('div');
        imageModalContainer.className = 'profile-image-modal';
        imageModalContainer.style.display = 'none';
        document.documentElement.appendChild(imageModalContainer);

        // CSS für Modal-Bild hinzufügen
        const style = document.createElement('style');
        style.textContent = `
            .profile-image-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100vw;
                height: 100vh;
                background-color: rgba(0, 0, 0, 0.8);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 0;
            }

            .modal-image-container {
                position: relative;
                z-index: 2001;
            }

            .profile-image-modal .modal-image {
                width: 500px;
                height: 500px;
                max-width: 80vw;
                max-height: 80vh;
                border-radius: 50%;
                object-fit: cover;
                border: 6px solid #fbc99a;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
                cursor: default;
            }

            .profile-image-modal .modal-close {
                position: absolute;
                top: -10px;
                right: -10px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .profile-image-modal .modal-close:hover {
                background: rgba(0, 0, 0, 0.9);
            }

            @media (max-width: 768px) {
                .profile-image-modal .modal-image {
                    width: 350px;
                    height: 350px;
                    max-width: 85vw;
                    max-height: 85vh;
                }
            }

            @media (max-width: 480px) {
                .profile-image-modal .modal-image {
                    width: 300px;
                    height: 300px;
                    max-width: 90vw;
                    max-height: 90vh;
                }
            }
        `;
        document.head.appendChild(style);

        // Klick-Ereignis für das Profilbild hinzufügen
        profileImage.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Modal-Container mit Bild füllen und anzeigen
            imageModalContainer.innerHTML = `
                <div class="modal-image-container">
                    <img src="${profileImage.src}" alt="${profileImage.alt}" class="modal-image">
                    <button class="modal-close" aria-label="Schließen">&times;</button>
                </div>`;

            // Prevent page shift by adding padding to compensate for scrollbar
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
            document.body.style.paddingRight = scrollbarWidth + 'px';
            document.body.style.overflow = 'hidden';

            imageModalContainer.style.display = 'flex';

            // Event-Handler für das Schließen hinzufügen
            function imageModalClickHandler(e) {
                // Nicht schließen, wenn auf das Bild oder den Schließen-Button geklickt wurde
                if (e.target.classList.contains('modal-image') || e.target.classList.contains('modal-close')) {
                    if (e.target.classList.contains('modal-close')) {
                        closeImageModal();
                    }
                    return;
                }

                // Schließen bei Klick auf Hintergrund
                closeImageModal();
            }

            imageModalContainer.addEventListener('click', imageModalClickHandler);

            // Event-Handler für den Schließen-Button
            const closeButton = imageModalContainer.querySelector('.modal-close');
            if (closeButton) {
                closeButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    closeImageModal();
                });
            }

            // Funktion zum Schließen des Bild-Modals
            function closeImageModal() {
                imageModalContainer.style.display = 'none';

                // Restore scrolling and remove padding
                document.body.style.overflow = 'auto';
                document.body.style.paddingRight = '';

                // Event-Listener entfernen
                imageModalContainer.removeEventListener('click', imageModalClickHandler);
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && imageModalContainer.style.display === 'flex') {
                imageModalContainer.style.display = 'none';
                document.body.style.overflow = 'auto';
                document.body.style.paddingRight = '';
            }
        });
    }
}









// Handle window resize for responsive elements
window.addEventListener('resize', function() {
    // Close profile image modal if open and screen size changes significantly
    const profileImageModal = document.querySelector('.profile-image-modal');
    if (profileImageModal && profileImageModal.style.display === 'flex') {
        if (window.innerWidth < 768) {
            profileImageModal.style.display = 'none';
            document.body.style.overflow = 'auto';
            document.body.style.paddingRight = '';
        }
    }
}, { passive: true });

// Optimize performance for mobile devices
function optimizeForMobile() {
    // Detect mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // Add mobile class for CSS optimizations
        document.body.classList.add('mobile-device');

        // Optimize touch scrolling
        document.body.style.webkitOverflowScrolling = 'touch';

        // Prevent zoom on double tap for better UX
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, { passive: false });
    }
}

// Setup Accordion functionality
function setupAccordion() {
    // Initialize accordion if present
    const accordionHeaders = document.querySelectorAll('.accordion-header');

    accordionHeaders.forEach(header => {
        // Add keyboard accessibility
        header.setAttribute('tabindex', '0');
        header.setAttribute('role', 'button');
        header.setAttribute('aria-expanded', 'false');

        // Add keyboard support
        header.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleAccordion(this);
            }
        });
    });
}

// Toggle accordion function (called from HTML onclick)
function toggleAccordion(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.accordion-icon');
    const isActive = header.classList.contains('active');

    if (isActive) {
        // Close accordion
        header.classList.remove('active');
        content.classList.remove('active');
        header.setAttribute('aria-expanded', 'false');
        icon.textContent = '+';
        content.style.maxHeight = '0';
    } else {
        // Open accordion
        header.classList.add('active');
        content.classList.add('active');
        header.setAttribute('aria-expanded', 'true');
        icon.textContent = '−';
        content.style.maxHeight = content.scrollHeight + 'px';
    }
}

// Setup Scroll Indicators
function setupScrollIndicators() {
    // Add smooth scrolling behavior to scroll arrows
    const scrollArrows = document.querySelectorAll('.scroll-arrow');

    scrollArrows.forEach(arrow => {
        arrow.addEventListener('click', scrollToContent);
    });
}

// Smooth scroll to content function
function scrollToContent() {
    // Find the first content section after the hero banner
    const banner = document.querySelector('.banner');
    if (banner) {
        const nextSection = banner.nextElementSibling;
        if (nextSection) {
            nextSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else {
            // Fallback: scroll down by viewport height
            window.scrollBy({
                top: window.innerHeight,
                behavior: 'smooth'
            });
        }
    }
}